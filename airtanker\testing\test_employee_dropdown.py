#!/usr/bin/env python3
"""
Test script for employee dropdown functionality in timesheet entries display.
This script creates test data with missing EmployeeID and PossibleEmployees
to verify the dropdown functionality works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask, session
from endpoints.timesheet_wizard import airtanker_app
import json

def create_test_data():
    """Create test data with various employee scenarios"""
    test_data = [
        # Normal employee with ID
        {
            'Date': '2025-06-16',
            'EmployeeName': '<PERSON>',
            'CustomerName': 'ABC Corp',
            'ProjectID': '21001',
            'WorkOrderID': '3001',
            'CustomerReportedHours': 8.0,
            'RateTypeID': 1,
            'RateType': 'REG',
            'Notes': 'Regular work',
            'ErrorMessage': '',
            'CustomerID': 101,
            'EmployeeID': 1001,
            'PossibleEmployees': None,
            'ProjectName': 'Project Alpha',
            'WorkOrderNumber': 'WO-3001',
            'FileName': 'test_timesheet.xlsx',
            'FileID': 1,
            'TaskID': None,
            'LocationID': None,
            'PossibleCustomerIDs': None,
            'PossibleWorkOrders': None
        },
        # Employee without ID but with possible employees
        {
            'Date': '2025-06-17',
            'EmployeeName': 'Jane Smith',
            'CustomerName': 'XYZ Inc',
            'ProjectID': '21002',
            'WorkOrderID': '3002',
            'CustomerReportedHours': 6.5,
            'RateTypeID': 1,
            'RateType': 'REG',
            'Notes': 'Project work',
            'ErrorMessage': 'Multiple employees found for Jane Smith in active work orders, or low confidence match. Please select the correct employee or skip this record.',
            'CustomerID': 102,
            'EmployeeID': None,
            'PossibleEmployees': [
                {'EmployeeID': 461, 'EmployeeName': 'Jane Smith (Engineering)'},
                {'EmployeeID': 462, 'EmployeeName': 'Jane Smith (Operations)'},
                {'EmployeeID': 463, 'EmployeeName': 'Jane A. Smith'}
            ],
            'ProjectName': 'Project Beta',
            'WorkOrderNumber': 'WO-3002',
            'FileName': 'test_timesheet.xlsx',
            'FileID': 1,
            'TaskID': None,
            'LocationID': None,
            'PossibleCustomerIDs': None,
            'PossibleWorkOrders': None
        },
        # Employee without ID and no possible employees
        {
            'Date': '2025-06-18',
            'EmployeeName': 'Unknown Worker',
            'CustomerName': 'DEF Ltd',
            'ProjectID': '21003',
            'WorkOrderID': '3003',
            'CustomerReportedHours': 4.0,
            'RateTypeID': 2,
            'RateType': 'OT',
            'Notes': 'Overtime work',
            'ErrorMessage': 'Employee not found with active work order, and predictive score is too low to recommend name matches.',
            'CustomerID': 103,
            'EmployeeID': None,
            'PossibleEmployees': None,
            'ProjectName': 'Project Gamma',
            'WorkOrderNumber': 'WO-3003',
            'FileName': 'test_timesheet.xlsx',
            'FileID': 1,
            'TaskID': None,
            'LocationID': None,
            'PossibleCustomerIDs': None,
            'PossibleWorkOrders': None
        },
        # Another employee with single possible match
        {
            'Date': '2025-06-19',
            'EmployeeName': 'Luis Alonso',
            'CustomerName': 'GHI Corp',
            'ProjectID': '21004',
            'WorkOrderID': '3004',
            'CustomerReportedHours': 7.5,
            'RateTypeID': 1,
            'RateType': 'REG',
            'Notes': 'Regular work',
            'ErrorMessage': 'Employee Luis Alonso not found on active work orders. Selected Luis Alonso from database. Please make sure the employee has an active work order and reupload file. Or skip this record.',
            'CustomerID': 104,
            'EmployeeID': None,
            'PossibleEmployees': [
                {'EmployeeID': 461, 'EmployeeName': 'Luis Alonso Padilla Gonzalez'}
            ],
            'ProjectName': 'Project Delta',
            'WorkOrderNumber': 'WO-3004',
            'FileName': 'test_timesheet.xlsx',
            'FileID': 1,
            'TaskID': None,
            'LocationID': None,
            'PossibleCustomerIDs': None,
            'PossibleWorkOrders': None
        }
    ]
    return test_data

def test_employee_dropdown():
    """Test the employee dropdown functionality"""
    print("Creating test data for employee dropdown functionality...")
    
    test_data = create_test_data()
    
    print(f"Created {len(test_data)} test entries:")
    for i, entry in enumerate(test_data, 1):
        print(f"  {i}. {entry['EmployeeName']} - EmployeeID: {entry['EmployeeID']} - PossibleEmployees: {len(entry['PossibleEmployees']) if entry['PossibleEmployees'] else 0}")
    
    print("\nTest data structure:")
    print("1. John Doe - Has EmployeeID (normal case)")
    print("2. Jane Smith - No EmployeeID, multiple possible employees (dropdown should appear)")
    print("3. Unknown Worker - No EmployeeID, no possible employees (dimmed text only)")
    print("4. Luis Alonso - No EmployeeID, single possible employee (dropdown should appear)")
    
    print("\nTo test this functionality:")
    print("1. Start the Flask application")
    print("2. Navigate to the timesheet entries display page")
    print("3. Look for entries with dimmed employee names and dropdowns")
    print("4. Select an employee from the dropdown")
    print("5. Verify the name becomes normal and error message clears")
    
    return test_data

if __name__ == "__main__":
    test_data = test_employee_dropdown()
    
    # Save test data to a JSON file for manual testing
    with open('employee_dropdown_test_data.json', 'w') as f:
        json.dump(test_data, f, indent=2)
    
    print(f"\nTest data saved to 'employee_dropdown_test_data.json'")
    print("You can use this data to manually test the employee dropdown functionality.")
