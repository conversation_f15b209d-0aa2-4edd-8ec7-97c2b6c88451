<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Dropdown Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .employee-name-dimmed {
            color: #6c757d;
            font-style: italic;
        }
        
        .employee-dropdown {
            font-size: 0.85rem;
            padding: 0.25rem 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: #fff;
            min-width: 150px;
        }
        
        .employee-dropdown:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Employee Dropdown Functionality Demo</h1>
        <p class="text-muted">This demonstrates how the employee dropdown works when Employee<PERSON> is missing.</p>
        
        <div class="row">
            <div class="col-12">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Employee</th>
                            <th>Customer</th>
                            <th>Hours</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Normal employee with ID -->
                        <tr>
                            <td>2025-06-16</td>
                            <td><span title="1001">John Doe</span></td>
                            <td>ABC Corp</td>
                            <td>8.00</td>
                            <td><span class="badge bg-success">Valid</span></td>
                        </tr>
                        
                        <!-- Employee without ID but with possible employees -->
                        <tr>
                            <td>2025-06-17</td>
                            <td>
                                <div>
                                    <span class="employee-name-dimmed">Jane Smith</span><br>
                                    <select class="employee-dropdown" onchange="handleEmployeeSelection(this)">
                                        <option value="">Select Employee...</option>
                                        <option value="461">Jane Smith (Engineering)</option>
                                        <option value="462">Jane Smith (Operations)</option>
                                        <option value="463">Jane A. Smith</option>
                                    </select>
                                </div>
                            </td>
                            <td>XYZ Inc</td>
                            <td>6.50</td>
                            <td><span class="badge bg-warning">Error</span></td>
                        </tr>
                        
                        <!-- Employee without ID and no possible employees -->
                        <tr>
                            <td>2025-06-18</td>
                            <td><span class="employee-name-dimmed">Unknown Worker</span></td>
                            <td>DEF Ltd</td>
                            <td>4.00</td>
                            <td><span class="badge bg-danger">Error</span></td>
                        </tr>
                        
                        <!-- Employee with single possible match -->
                        <tr>
                            <td>2025-06-19</td>
                            <td>
                                <div>
                                    <span class="employee-name-dimmed">Luis Alonso</span><br>
                                    <select class="employee-dropdown" onchange="handleEmployeeSelection(this)">
                                        <option value="">Select Employee...</option>
                                        <option value="461">Luis Alonso Padilla Gonzalez</option>
                                    </select>
                                </div>
                            </td>
                            <td>GHI Corp</td>
                            <td>7.50</td>
                            <td><span class="badge bg-warning">Error</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>How it works:</h3>
                <ul>
                    <li><strong>Normal employees</strong> (like John Doe) show normally with their EmployeeID in the title</li>
                    <li><strong>Employees without ID but with possible matches</strong> (like Jane Smith) show dimmed with a dropdown to select the correct employee</li>
                    <li><strong>Employees without ID and no matches</strong> (like Unknown Worker) show dimmed text only</li>
                    <li>When you select an employee from the dropdown, the name becomes normal and the error status should clear</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function handleEmployeeSelection(selectElement) {
            const selectedEmployeeID = selectElement.value;
            const selectedEmployeeName = selectElement.options[selectElement.selectedIndex].text;
            
            if (selectedEmployeeID) {
                // Update the cell display to show the selected employee normally
                const cell = selectElement.closest('td');
                cell.innerHTML = `<span title="${selectedEmployeeID}">${selectedEmployeeName}</span>`;
                
                // Update the status to valid
                const statusCell = selectElement.closest('tr').querySelector('.badge');
                statusCell.className = 'badge bg-success';
                statusCell.textContent = 'Valid';
                
                console.log(`Employee selected: ${selectedEmployeeName} (ID: ${selectedEmployeeID})`);
            }
        }
    </script>
</body>
</html>
